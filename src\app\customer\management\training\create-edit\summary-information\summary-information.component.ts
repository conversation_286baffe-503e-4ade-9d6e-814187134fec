import { Component, Injector, Input, OnInit } from "@angular/core";
import { ViewComponent } from "@core/inheritance/app-component.base";
import { TrainingDto } from "@proxies/training.proxy";
import { TrainingSubscriptionServiceProxy, TrainingAttendeeDto } from "@proxies/training-subscription.proxy";
import { finalize } from "rxjs";

@Component({
    selector: 'app-training-summary-information',
    templateUrl: 'summary-information.component.html',
    styleUrls: [
        'summary-information.component.scss'
    ]
})
export class TrainingSummaryInformationComponent extends ViewComponent implements OnInit {

    @Input() training!: TrainingDto;
    
    attendees: TrainingAttendeeDto[] = [];
    isLoading: boolean = false;
    
    // Estadísticas
    totalAttendees: number = 0;
    attendedCount: number = 0;
    notAttendedCount: number = 0;
    watchedVideoCount: number = 0;
    completedEvaluationCount: number = 0;
    averageScore: number = 0;

    private trainingSubscriptionServiceProxy: TrainingSubscriptionServiceProxy;

    constructor(_injector: Injector) {
        super(_injector);
        this.trainingSubscriptionServiceProxy = _injector.get(TrainingSubscriptionServiceProxy);
    }

    ngOnInit(): void {
        if (this.training?.id) {
            this.loadAttendees();
        }
    }

    private loadAttendees(): void {
        this.isLoading = true;
        this.trainingSubscriptionServiceProxy.getAllTrainingAttendees(this.training.id)
            .pipe(finalize(() => this.isLoading = false))
            .subscribe({
                next: (response) => {
                    this.attendees = response.items;
                    this.calculateStatistics();
                },
                error: (error) => {
                    console.error('Error loading attendees:', error);
                    this.message.error('Error al cargar el resumen de asistencias', 'Error');
                }
            });
    }

    private calculateStatistics(): void {
        this.totalAttendees = this.attendees.length;
        
        this.attendedCount = this.attendees.filter(a => a.attended?.toLowerCase() === 'sí' || a.attended?.toLowerCase() === 'yes').length;
        this.notAttendedCount = this.totalAttendees - this.attendedCount;
        
        this.watchedVideoCount = this.attendees.filter(a => a.watchedVideo?.toLowerCase() === 'sí' || a.watchedVideo?.toLowerCase() === 'yes').length;
        
        this.completedEvaluationCount = this.attendees.filter(a => a.completedEvaluation?.toLowerCase() === 'sí' || a.completedEvaluation?.toLowerCase() === 'yes').length;
        
        // Calcular promedio de calificaciones (solo de quienes completaron la evaluación)
        const scoresWithEvaluation = this.attendees.filter(a => 
            (a.completedEvaluation?.toLowerCase() === 'sí' || a.completedEvaluation?.toLowerCase() === 'yes') && 
            a.evaluationScore > 0
        );
        
        if (scoresWithEvaluation.length > 0) {
            const totalScore = scoresWithEvaluation.reduce((sum, attendee) => sum + attendee.evaluationScore, 0);
            this.averageScore = Math.round((totalScore / scoresWithEvaluation.length) * 100) / 100;
        } else {
            this.averageScore = 0;
        }
    }

    getAttendancePercentage(): number {
        if (this.totalAttendees === 0) return 0;
        return Math.round((this.attendedCount / this.totalAttendees) * 100);
    }

    getVideoWatchedPercentage(): number {
        if (this.totalAttendees === 0) return 0;
        return Math.round((this.watchedVideoCount / this.totalAttendees) * 100);
    }

    getEvaluationCompletedPercentage(): number {
        if (this.totalAttendees === 0) return 0;
        return Math.round((this.completedEvaluationCount / this.totalAttendees) * 100);
    }

    getAttendanceStatusClass(attended: string): string {
        const status = attended?.toLowerCase();
        if (status === 'sí' || status === 'yes') {
            return 'badge-success';
        } else if (status === 'no') {
            return 'badge-danger';
        }
        return 'badge-secondary';
    }

    getAttendanceStatusText(attended: string): string {
        const status = attended?.toLowerCase();
        if (status === 'sí' || status === 'yes') {
            return 'Asistió';
        } else if (status === 'no') {
            return 'No asistió';
        }
        return 'Pendiente';
    }

    getEvaluationStatusClass(completed: string): string {
        const status = completed?.toLowerCase();
        if (status === 'sí' || status === 'yes') {
            return 'badge-success';
        } else if (status === 'no') {
            return 'badge-danger';
        }
        return 'badge-secondary';
    }

    getEvaluationStatusText(completed: string): string {
        const status = completed?.toLowerCase();
        if (status === 'sí' || status === 'yes') {
            return 'Completada';
        } else if (status === 'no') {
            return 'No completada';
        }
        return 'Pendiente';
    }

    getScoreClass(score: number): string {
        if (score >= 70) {
            return 'score-excellent';
        } else if (score >= 60) {
            return 'score-good';
        } else if (score > 0) {
            return 'score-poor';
        }
        return 'score-none';
    }

    exportToExcel(): void {
        // Implementar exportación a Excel si es necesario
        this.notify.info('Funcionalidad de exportación en desarrollo', 3000);
    }

    refreshData(): void {
        this.loadAttendees();
    }
}
