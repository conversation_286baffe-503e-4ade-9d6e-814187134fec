<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="loaded ? (completed ? 'Visualizar capacitación' : (id ? 'Editar capacitación' : 'Crear capacitación')) : 'Cargando...'" [disabled]="disabled" size="extra">
        <app-modal-body>
            <p-tabView *ngIf="loaded" [(activeIndex)]="activeIndex" [scrollable]="true">
                <p-tabPanel header="Información general">
                    <app-training-general-information
                        [modalForm]="modalForm"
                        [training]="item"
                        [trainingAssistenceCode]="trainingAssistenceCode"
                        [trainingTypes]="trainingTypes"
                        [trainingModes]="trainingModes"
                        [centers]="centers"
                        [(trainingDate)]="trainingDate"
                        [(trainingStartTime)]="trainingStartTime"
                        [(trainingEndTime)]="trainingEndTime"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel header="Colaboradores">
                    <app-training-customer-information
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel header="Invitados">
                    <app-traingin-subscription-information
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel header="Inscritos">
                    <app-training-inscription-information
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel header="Asistencia">
                    <app-training-assistance-information
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel header="Finalización">
                    <app-training-complete-information
                        [modalForm]="modalForm"
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel *ngIf="item?.isObligatory === true" header="Evaluaciones">
                    <app-training-evaluation-information
                        [modalForm]="modalForm"
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                   <p-tabPanel *ngIf="completed && item?.isObligatory " header="Resumen">
                    <app-training-summary-information
                        [training]="item"/>
                </p-tabPanel>
                <p-tabPanel header="Satisfacción">
                    <app-training-calification-information
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel header="Historial">
                    <app-training-operation-information
                        [training]="item"
                        [completed]="completed"/>
                </p-tabPanel>
           
            </p-tabView>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" [showSaveButton]="!completed" />
    </app-modal>
</form>