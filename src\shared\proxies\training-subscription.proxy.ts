import { mergeMap } from 'rxjs/operators';
import { Injectable, Injector } from "@angular/core";
import { Observable, of } from "rxjs";
import { AppHttpRequestService } from '@core/services/http.request.service';
import { IJsonConvertable, PagedResultDto } from '@core/models/mappings';
 import { DateTime } from 'luxon';

@Injectable()
export class TrainingSubscriptionServiceProxy {
    private request: AppHttpRequestService;

    constructor(_injector: Injector) {
        this.request = _injector.get(AppHttpRequestService);
    }

    getAllTrainingAttendees(trainingId: number): Observable<PagedResultDto<TrainingAttendeeDto>> {
        let url = '/api/services/app/TrainingSubscription/GetAllTrainingAttendees?';
        if (trainingId !== null && trainingId !== undefined)
            url += "TrainingId=" + encodeURIComponent("" + trainingId) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new PagedResultDto<TrainingAttendeeDto>().fromJS(data, TrainingAttendeeDto))));
    }
}

export class TrainingAttendeeDto implements IJsonConvertable<TrainingAttendeeDto> {
    id!: number;
    name!: string;
    emailAddress!: string;
    attendanceType!: string;
    watchedVideo!: string;
    watchedVideoTime!: DateTime;
    completedEvaluation!: string;
    evaluationScore!: number;
    attended!: string;

    constructor(data?: ITrainingAttendeeDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.name = data["name"];
            this.emailAddress = data["emailAddress"];
            this.attendanceType = data["attendanceType"];
            this.watchedVideo = data["watchedVideo"];
            this.watchedVideoTime = data["watchedVideoTime"] ? DateTime.fromISO(data["watchedVideoTime"]) : <any>undefined;
            this.completedEvaluation = data["completedEvaluation"];
            this.evaluationScore = data["evaluationScore"];
            this.attended = data["attended"];
        }
    }

    static fromJS(data: any): TrainingAttendeeDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingAttendeeDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingAttendeeDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["emailAddress"] = this.emailAddress;
        data["attendanceType"] = this.attendanceType;
        data["watchedVideo"] = this.watchedVideo;
        data["watchedVideoTime"] = this.watchedVideoTime ? this.watchedVideoTime.toJSON() : <any>undefined;
        data["completedEvaluation"] = this.completedEvaluation;
        data["evaluationScore"] = this.evaluationScore;
        data["attended"] = this.attended;
        return JSON.stringify(data);
    }
}

export interface ITrainingAttendeeDto {
    id: number;
    name: string;
    emailAddress: string;
    attendanceType: string;
    watchedVideo: string;
    watchedVideoTime: DateTime;
    completedEvaluation: string;
    evaluationScore: number;
    attended: string;
}
