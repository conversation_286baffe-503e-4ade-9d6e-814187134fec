<!-- Loading indicator -->
<div *ngIf="isLoading" class="col-12 text-center mb-4">
    <ion-spinner name="crescent"></ion-spinner>
    <p class="mt-2">Cargando evaluación...</p>
</div>

<form [formGroup]="evaluationForm" class="row" [style.opacity]="isLoading ? '0.5' : '1'">

    <!-- Información de la evaluación -->
    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de la evaluación
        </label>
        <hr>
    </div>

    <div class="col-12 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label for="EvaluationTitle" class="form-label">
                    Título de la evaluación (*)
                </label>
                <input type="text" class="form-control" id="EvaluationTitle"
                       name="EvaluationTitle" formControlName="evaluationTitle"
                       [disabled]="!canEdit" maxlength="255" />
                <div class="input-length">
                    {{evaluationForm.get('evaluationTitle')?.value?.length || 0}}/255
                </div>
            </div>
        </div>
    </div>

 

    <div class="col-12 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label for="EvaluationDescription" class="form-label">
                    Descripción de la evaluación (*)
                </label>
                <textarea class="form-control" id="EvaluationDescription"
                          name="EvaluationDescription" formControlName="evaluationDescription"
                          [disabled]="!canEdit" rows="3" maxlength="1000"></textarea>
                <div class="input-length">
                    {{evaluationForm.get('evaluationDescription')?.value?.length || 0}}/1000
                </div>
            </div>
        </div>
    </div>

    <!-- Preguntas -->
    <div class="col-12 mb-3">
        <div class="d-flex justify-content-between align-items-center">
            <label class="fz-normal fw-bold text-dark mb-0">
                Preguntas de la evaluación
            </label>
     
        </div>
        <hr>
    </div>

    <!-- Lista de preguntas -->
    <div formArrayName="questions">
        <div *ngFor="let question of questions.controls; let i = index" [formGroupName]="i" class="col-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Pregunta {{i + 1}}</h6>
                    <div class="d-flex gap-2">
                        <ion-button *ngIf="canEdit && questions.length > 1"
                                    (click)="removeQuestion(i)"
                                    color="danger"
                                    fill="clear"
                                    size="small">
                           
                        </ion-button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Texto de la pregunta -->
                        <div class="col-12 mb-3">
                            <div class="ion-input">
                                <div class="input-control">
                                    <label class="form-label">
                                        Texto de la pregunta (*)
                                    </label>
                                    <textarea class="form-control"
                                              formControlName="questionText"
                                              [disabled]="!canEdit"
                                              rows="2"
                                              maxlength="500"></textarea>
                                    <div class="input-length">
                                        {{question.get('questionText')?.value?.length || 0}}/500
                                    </div>
                                </div>
                            </div>
                        </div>
 

                        <!-- Opciones de respuesta -->
                        <div class="col-12">
                            <label class="form-label mb-2">
                                Opciones de respuesta (*) -
                                <small class="text-muted">
                                    Seleccione una respuesta correcta
                                </small>
                            </label>
                            <div formArrayName="options">
                                <div *ngFor="let option of getQuestionOptions(i).controls; let j = index"
                                     [formGroupName]="j"
                                     class="d-flex align-items-center mb-2">

                                    <div class="form-check me-3">
                                        <input class="form-check-input"
                                               type="radio"
                                               [name]="'correct_' + i"
                                               [checked]="option.get('isCorrect')?.value === true"
                                               [disabled]="!canEdit"
                                               (change)="onCorrectAnswerChange(i, j)" />
                                        <label class="form-check-label">
                                            Correcta
                                        </label>
                                    </div>

                                    <div class="flex-grow-1">
                                        <input type="text"
                                               class="form-control"
                                               formControlName="optionText"
                                               [disabled]="!canEdit"
                                               [placeholder]="'Opción ' "
                                               maxlength="255" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botón de guardar -->
    <div *ngIf="canEdit" class="col-12 d-flex justify-content-end">
        <ion-button (click)="saveEvaluation()" color="success" fill="solid" [disabled]="isLoading">
            <ion-icon slot="start" name="save"></ion-icon>
            <ng-container *ngIf="!isLoading">
                {{evaluationData?.id ? 'Actualizar evaluación' : 'Crear evaluación'}}
            </ng-container>
            <ng-container *ngIf="isLoading">
                {{evaluationData?.id ? 'Actualizando...' : 'Creando...'}}
            </ng-container>
        </ion-button>
    </div>

    <!-- Información adicional -->
    <div class="col-12 mt-4">
        <div class="alert alert-info" role="alert">
            <h6><ion-icon name="information-circle"></ion-icon> Información importante:</h6>
            <ul class="mb-0">
                <li>Cada pregunta debe tener exactamente una respuesta correcta</li>
                <li>La suma total de puntos de todas las preguntas debe ser 100</li>
                <li>Los participantes deben obtener al menos 70 puntos para aprobar</li>
                <li>La evaluación estará disponible después de completar la capacitación</li>
            </ul>
        </div>
    </div>

</form>
